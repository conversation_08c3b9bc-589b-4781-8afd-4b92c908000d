import os
import subprocess
import csv
from collections import defaultdict

# Set this to your directory containing all Bitbucket repos
BASE_DIR = "/Users/<USER>/projects/veefin"
OUTPUT_FILE = "bitbucket_loc_report.csv"


def is_git_repo(path):
    return os.path.isdir(os.path.join(path, ".git"))


def get_authors(repo_path):
    try:
        output = subprocess.check_output(
            ["git", "log", "--pretty=format:%an"],
            cwd=repo_path,
            stderr=subprocess.DEVNULL,
            text=True,
        )
        return list(set(output.splitlines()))
    except subprocess.CalledProcessError:
        return []


def get_author_stats(repo_path, author):
    try:
        output = subprocess.check_output(
            ["git", "log", f"--author={author}", "--pretty=tformat:", "--numstat"],
            cwd=repo_path,
            stderr=subprocess.DEVNULL,
            text=True,
        )
        added = removed = 0
        for line in output.splitlines():
            parts = line.strip().split()
            if len(parts) >= 2 and parts[0].isdigit() and parts[1].isdigit():
                added += int(parts[0])
                removed += int(parts[1])
        return added, removed
    except subprocess.CalledProcessError:
        return 0, 0


def scan_repos(base_dir):
    report_data = []

    for repo_name in os.listdir(base_dir):
        repo_path = os.path.join(base_dir, repo_name)
        print(f"Scanning {repo_name}")
        if not is_git_repo(repo_path):
            continue

        authors = get_authors(repo_path)
        for author in authors:
            added, removed = get_author_stats(repo_path, author)
            net = added - removed
            report_data.append(
                {
                    "Repository": repo_name,
                    "Author": author,
                    "Added": added,
                    "Removed": removed,
                    "Net": net,
                }
            )

    return report_data


def write_csv(data, file_path):
    if not data:
        print("No data to write.")
        return
    with open(file_path, "w", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
    print(f"✅ Report written to {file_path}")


if __name__ == "__main__":
    data = scan_repos(BASE_DIR)
    write_csv(data, OUTPUT_FILE)
