import os
import subprocess
import csv
from collections import defaultdict
from datetime import datetime, timedelta
import argparse

# Set this to your directory containing all Bitbucket repos
BASE_DIR = "/Users/<USER>/projects/veefin"
OUTPUT_FILE = "bitbucket_loc_report.csv"


def is_git_repo(path):
    return os.path.isdir(os.path.join(path, ".git"))


def parse_date(date_str):
    """Parse date string in YYYY-MM-DD format."""
    try:
        return datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Use YYYY-MM-DD format.")


def generate_time_periods(start_date, end_date, period_type):
    """Generate list of time periods between start and end dates."""
    periods = []
    current = start_date

    while current <= end_date:
        if period_type == "week":
            # Start of week (Monday)
            period_start = current - timedelta(days=current.weekday())
            period_end = period_start + timedelta(days=6)
            # Don't go beyond end_date
            if period_end > end_date:
                period_end = end_date
            periods.append((period_start, period_end))
            current = period_end + timedelta(days=1)
        elif period_type == "month":
            # Start of month
            period_start = current.replace(day=1)
            # End of month
            if period_start.month == 12:
                next_month = period_start.replace(year=period_start.year + 1, month=1)
            else:
                next_month = period_start.replace(month=period_start.month + 1)
            period_end = next_month - timedelta(days=1)
            # Don't go beyond end_date
            if period_end > end_date:
                period_end = end_date
            periods.append((period_start, period_end))
            current = next_month

    return periods


def format_period(start_date, end_date):
    """Format time period for display."""
    return f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"


def get_authors(repo_path):
    try:
        output = subprocess.check_output(
            ["git", "log", "--pretty=format:%an"],
            cwd=repo_path,
            stderr=subprocess.DEVNULL,
            text=True,
        )
        return list(set(output.splitlines()))
    except subprocess.CalledProcessError:
        return []


def get_author_stats(repo_path, author, start_date=None, end_date=None):
    try:
        cmd = ["git", "log", f"--author={author}", "--pretty=tformat:", "--numstat"]

        # Add date filtering if provided
        if start_date and end_date:
            since_str = start_date.strftime("%Y-%m-%d")
            until_str = end_date.strftime("%Y-%m-%d")
            cmd.extend([f"--since={since_str}", f"--until={until_str}"])

        output = subprocess.check_output(
            cmd,
            cwd=repo_path,
            stderr=subprocess.DEVNULL,
            text=True,
        )
        added = removed = 0
        for line in output.splitlines():
            parts = line.strip().split()
            if len(parts) >= 2 and parts[0].isdigit() and parts[1].isdigit():
                added += int(parts[0])
                removed += int(parts[1])
        return added, removed
    except subprocess.CalledProcessError:
        return 0, 0


def scan_repos(base_dir, periods=None, group_by=None):
    """
    Scan repositories for LOC statistics.

    Args:
        base_dir: Base directory containing repositories
        periods: List of (start_date, end_date) tuples for time-based reporting
        group_by: 'author', 'repo', or None for grouping strategy
    """
    report_data = []

    for repo_name in os.listdir(base_dir):
        repo_path = os.path.join(base_dir, repo_name)
        print(f"Scanning {repo_name}")
        if not is_git_repo(repo_path):
            continue

        authors = get_authors(repo_path)

        if periods:
            # Time-based reporting
            for start_date, end_date in periods:
                period_str = format_period(start_date, end_date)

                if group_by == "repo":
                    # Aggregate by repository across all authors
                    total_added = total_removed = 0
                    for author in authors:
                        added, removed = get_author_stats(
                            repo_path, author, start_date, end_date
                        )
                        total_added += added
                        total_removed += removed

                    if (
                        total_added > 0 or total_removed > 0
                    ):  # Only include periods with activity
                        report_data.append(
                            {
                                "Period": period_str,
                                "Repository": repo_name,
                                "Author": "ALL_AUTHORS",
                                "Added": total_added,
                                "Removed": total_removed,
                                "Net": total_added - total_removed,
                            }
                        )
                else:
                    # Individual author reporting (default)
                    for author in authors:
                        added, removed = get_author_stats(
                            repo_path, author, start_date, end_date
                        )
                        if (
                            added > 0 or removed > 0
                        ):  # Only include periods with activity
                            net = added - removed
                            report_data.append(
                                {
                                    "Period": period_str,
                                    "Repository": repo_name,
                                    "Author": author,
                                    "Added": added,
                                    "Removed": removed,
                                    "Net": net,
                                }
                            )
        else:
            # Legacy mode - all time statistics
            for author in authors:
                added, removed = get_author_stats(repo_path, author)
                net = added - removed
                report_data.append(
                    {
                        "Repository": repo_name,
                        "Author": author,
                        "Added": added,
                        "Removed": removed,
                        "Net": net,
                    }
                )

    return report_data


def group_by_author(data):
    """Group data by author across all repositories for each time period."""
    grouped = {}

    for row in data:
        period = row.get("Period", "All Time")
        author = row["Author"]

        # Initialize period if not exists
        if period not in grouped:
            grouped[period] = {}

        # Initialize author if not exists
        if author not in grouped[period]:
            grouped[period][author] = {
                "Added": 0,
                "Removed": 0,
                "Net": 0,
                "Repositories": set(),
            }

        grouped[period][author]["Added"] += row["Added"]
        grouped[period][author]["Removed"] += row["Removed"]
        grouped[period][author]["Net"] += row["Net"]
        grouped[period][author]["Repositories"].add(row["Repository"])

    # Convert back to list format
    result = []
    for period, authors in grouped.items():
        for author, stats in authors.items():
            repo_list = ", ".join(sorted(stats["Repositories"]))
            result.append(
                {
                    "Period": period if period != "All Time" else None,
                    "Repository": repo_list,
                    "Author": author,
                    "Added": stats["Added"],
                    "Removed": stats["Removed"],
                    "Net": stats["Net"],
                }
            )

    # Remove Period column if it's None (legacy mode)
    if result and result[0]["Period"] is None:
        for row in result:
            del row["Period"]

    return result


def write_csv(data, file_path):
    if not data:
        print("No data to write.")
        return
    with open(file_path, "w", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
    print(f"✅ Report written to {file_path}")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate Bitbucket LOC reports with flexible time-based analysis"
    )

    parser.add_argument(
        "--period",
        choices=["week", "month"],
        help="Time period type for reporting (week or month)",
    )

    parser.add_argument("--start-date", help="Start date in YYYY-MM-DD format")

    parser.add_argument(
        "--end-date", help="End date in YYYY-MM-DD format (defaults to current date)"
    )

    parser.add_argument(
        "--group-by",
        choices=["author", "repo"],
        help="Group data by author or repository",
    )

    parser.add_argument(
        "--output",
        default=OUTPUT_FILE,
        help=f"Output CSV file (default: {OUTPUT_FILE})",
    )

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_arguments()

    # Validate arguments
    if args.period and not args.start_date:
        print("Error: --start-date is required when using --period")
        return 1

    if args.group_by and not args.period:
        print("Error: --group-by requires --period to be specified")
        return 1

    periods = None
    if args.period and args.start_date:
        try:
            start_date = parse_date(args.start_date)
            end_date = parse_date(args.end_date) if args.end_date else datetime.now()

            if start_date > end_date:
                print("Error: Start date cannot be after end date")
                return 1

            periods = generate_time_periods(start_date, end_date, args.period)
            print(
                f"Generating {args.period}ly report from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
            )
            print(f"Found {len(periods)} {args.period}(s) to analyze")

        except ValueError as e:
            print(f"Error: {e}")
            return 1

    # Scan repositories
    data = scan_repos(BASE_DIR, periods, args.group_by)

    # Apply author grouping if requested
    if args.group_by == "author" and data:
        data = group_by_author(data)

    # Write output
    write_csv(data, args.output)

    if data:
        print(f"Generated report with {len(data)} entries")

    return 0


if __name__ == "__main__":
    exit(main())
